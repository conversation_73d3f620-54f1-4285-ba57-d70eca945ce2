[{"description": "", "elements": [{"description": "", "id": "upload;verify-user-can-upload-multiple-files", "keyword": "<PERSON><PERSON><PERSON>", "line": 25, "name": "Verify user can upload multiple files", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 49755000000}}, {"arguments": [], "keyword": "When ", "line": 26, "name": "The user uploads multiple files for processing", "match": {"location": "webpack://veritone-illuminate-app/cypress/e2e/step_definitions/upload/upload.spec.ts:29"}, "result": {"status": "passed", "duration": 81786000000}}], "tags": [{"name": "@e2e", "line": 24}, {"name": "@upload", "line": 24}], "type": "scenario"}], "id": "upload", "line": 1, "keyword": "Feature", "name": "Upload", "tags": [], "uri": "cypress\\e2e\\features\\upload.feature"}]