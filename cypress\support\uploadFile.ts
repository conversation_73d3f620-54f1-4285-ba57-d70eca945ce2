Cypress.Commands.add('OpenModalUploadFile', () => {
  cy.get('[data-test="upload-file"]').click();
  cy.get('[data-test="upload-media"]').click();
});

Cypress.Commands.add(
  'SelectFile',
  (fileName: string, videoPath: string, mimeType: string) => {
    cy.fixture(videoPath, 'binary', { timeout: 120000 })
      .then(Cypress.Blob.binaryStringToBlob)
      .then((fileContent) => {
        console.log('fileContent', fileContent);
        cy.get('input[type="file"]').attachFile({
          fileContent,
          fileName: fileName,
          mimeType: mimeType,
          encoding: 'utf8',
        });
        return;
      });
  }
);

Cypress.Commands.add('ClickToUpload', () => {
  cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();
});

Cypress.Commands.add('AddTagsUpload', () => {
  cy.get('[data-test="tags-upload"] input').type('demo{enter}');
});

Cypress.Commands.add('EditFile', (fileName: string) => {
  cy.get('[data-test="checked-all-file"]').find('[type="checkbox"]').check();
  cy.get('[data-test="edit-file"]').click();
  cy.get('[data-test="file-name-upload"] input').clear();
  cy.get('[data-test="file-name-upload"] input').type(fileName);
  cy.get('[data-testid="tags-edit"]').click();
  cy.AddTagsUpload();
  cy.contains('General').click();
  cy.get('[data-test="save-edit-file"]').click();
});

Cypress.Commands.add('FileUpload', (fileName, videoPath, mimeType) => {
  cy.OpenModalUploadFile();
  cy.SelectFile(fileName, videoPath, mimeType);
  cy.ClickToUpload();
  cy.EditFile(fileName);
});

Cypress.Commands.add('NextStep', () => {
  cy.get('[data-test="next-step"]').click();
});

Cypress.Commands.add(
  'ClickEngineCategory',
  (categoryIdTranscription: string) => {
    cy.get(
      `[data-test="click-engine-category_${categoryIdTranscription}"]`
    ).click();
  }
);

Cypress.Commands.add(
  'SelectAvailableEngine',
  (categoryId: string, engineId: string) => {
    cy.get('[data-test="select-available-engine"]').click();
    cy.get(`[data-test="list-available-engine_${categoryId}"]`).click();
    cy.get(`[data-test="add-engine-available_${engineId}"]`).click();
  }
);

Cypress.Commands.add('SaveTemplateEngine', () => {
  cy.get('[data-test="show-modal-save-template-engine"]').click();
  cy.get('[data-test="template-engine-name"] input').type(
    'test template engine'
  );
  cy.get('[data-test="save-template-engine"]').click();
});

Cypress.Commands.add('AddContentTemplate', () => {
  cy.get('[data-test="list-content-template"]').each(($el) => {
    if ($el.text() === 'Override') {
      cy.get(
        '[data-test="add-content-template_8c61fc4c-953f-4be8-b091-88e4069a9106"]'
      ).click();
      cy.get('[data-test="age"]').type('age');
      cy.get('[data-test="country"]').type('country');
      cy.get('[data-test="filename"]').type('filename');
    }
  });
});

Cypress.Commands.add('ClickSimpleCognitiveWorkflow', () => {
  cy.get('body').then(($body) => {
    if ($body.find('[data-test="show-simple-cognitive-workflow"]').length > 0) {
      // show simple cognitive workflow
      cy.get('[data-test="show-simple-cognitive-workflow"]').click();
    }
    return;
  });
});

Cypress.Commands.add('SelectFolder', () => {
  cy.get('[data-test="select-folder"]').click();
  cy.get('ul button')
    .filter((_index, element) => Cypress.$(element).text() === 'e2e')
    .within(() => {
      return cy.get('[data-testid="arrow-right-img"]').click();
    });
  cy.get('[data-test="list-folder"]').contains('upload').click();
  cy.get('[data-test=move-folder-submit-button]').click();
});

Cypress.Commands.add(
  'SelectLanguages',
  (sourceLanguage: string, targetLanguage: string) => {
    cy.get('[data-test="fields-engine_sourceLanguageCode"]').click();
    cy.get(`[data-test="${sourceLanguage}"]`).click();
    cy.get('[data-test="fields-engine_target"]').click();
    cy.get(`[data-test="${targetLanguage}"]`).click();
  }
);

Cypress.Commands.add(
  'UploadFileBasic',
  (
    fileName: string,
    filePath: string,
    mimeType: string,
    categoryId: string,
    engineId: string
  ) => {
    // upload file
    cy.FileUpload(fileName, filePath, mimeType);
    cy.NextStep();
    // use advanced mode
    cy.contains('Show Advanced Cognitive Workflow').click();
    // test select category
    cy.SelectAvailableEngine(categoryId, engineId);
    cy.NextStep();
    // next step content template
    cy.NextStep();
    // select folder e2e/upload
    cy.SelectFolder();
    // add tags upload
    cy.AddTagsUpload();
    // save upload file
    cy.NextStep();
    cy.contains('created successfully');
  }
);

Cypress.Commands.add(
  'UploadFileAdvanced',
  (fileName: string, filePath: string, mimeType: string, categoryEngines) => {
    // upload file
    cy.FileUpload(fileName, filePath, mimeType);
    // next step processing
    cy.NextStep();
    // use advanced mode
    cy.contains('Show Advanced Cognitive Workflow').click();
    // test select category
    categoryEngines.forEach(
      (categoryEngine: { categoryId: string; engineId: string }) => {
        cy.SelectAvailableEngine(
          categoryEngine.categoryId,
          categoryEngine.engineId
        );
      }
    );
    // save template engine
    cy.SaveTemplateEngine();

    cy.get('button').each(($el) => {
      if ($el.text() === 'Override') {
        cy.get('[data-test="confirm-button"]').click();
      }
    });
    // next step content template
    cy.NextStep();
    cy.AddContentTemplate();
    // next step customize
    cy.NextStep();
    // select folder e2e test
    cy.SelectFolder();
    // add tags upload
    cy.AddTagsUpload();
    // save upload file
    cy.NextStep();
    cy.contains('created successfully');
  }
);

Cypress.Commands.add(
  'UploadFileImageWithEngineFacialDetection',
  (
    fileName: string,
    videoPath: string,
    mimeType: string,
    categoryIdFacialDetection: string,
    engineIdFaceboxSimilarity: string
  ) => {
    // upload file
    cy.FileUpload(fileName, videoPath, mimeType);
    // next step processing
    cy.NextStep();
    // check mode simple
    cy.ClickSimpleCognitiveWorkflow();
    // test select category
    cy.ClickEngineCategory(categoryIdFacialDetection);
    // test unselect category
    cy.ClickEngineCategory(categoryIdFacialDetection);
    // show advenced cognitive workflow
    cy.get('[data-test="show-advanced-cognitive-workflow"]').click();
    // test select category translate
    cy.SelectAvailableEngine(
      categoryIdFacialDetection,
      engineIdFaceboxSimilarity
    );
    // select libraries
    cy.get('[data-test="select-libraries"]').click();
    cy.get('[data-test="list-libraries_California Known Offenders"]').click();
    // next step content template
    cy.NextStep();
    cy.AddContentTemplate();
    // next step customize
    cy.NextStep();
    // select folder e2e test
    cy.SelectFolder();
    // add tags upload
    cy.AddTagsUpload();
    // save upload file
    cy.NextStep();
  }
);

Cypress.Commands.add(
  'ReprocessBasic',
  (categoryId: string, engineId: string, fileName: string) => {
    cy.contains('FILES').click();
    cy.contains(fileName).parent().prev().click();
    cy.get('[data-test="reprocess-file-icon-button"]').click();
    cy.contains('Show Advanced Cognitive Workflow').click();
    cy.SelectAvailableEngine(categoryId, engineId);
    cy.get('[data-test="save-reprocess"]').click();
    cy.contains('created successfully');
  }
);

Cypress.Commands.add(
  'ReprocessAllWithEngineTranscription',
  (categoryIdTranscription) => {
    cy.get('[data-test="files-table-row"]').find('[type="checkbox"]').check();
    cy.get('[data-test="reprocess-file"]').click();
    cy.ClickEngineCategory(categoryIdTranscription);
    cy.get('[data-test="save-reprocess"]').click();
  }
);

Cypress.Commands.add(
  'ReprocessFileVideoWithEngineAmazonTranslate',
  (categoryIdTranslate, engineIdTranslate) => {
    // cy.get('[data-testid^=files-table-row]')
    //   .find('[type="checkbox"]')
    //   .first()
    //   .check();
    cy.get('[data-test="reprocess-file-icon-button"]').click();
    cy.SelectAvailableEngine(categoryIdTranslate, engineIdTranslate);
    cy.SelectLanguages('sw', 'en');
    cy.get('[data-test="save-reprocess"]').click();
    cy.contains('1 jobs are created successfully');
  }
);

Cypress.Commands.add(
  'UploadMultipleFilesBasic',
  (files: Array<{ fileName: string; filePath: string; mimeType: string }>) => {
    // Open upload modal
    cy.get('[data-test="upload-file"]').click();
    cy.get('[data-test="upload-media"]').click();

    // Select multiple files by attaching them to the file input
    files.forEach((file) => {
      cy.fixture(file.filePath, 'binary', { timeout: 120000 })
        .then(Cypress.Blob.binaryStringToBlob)
        .then((fileContent) => {
          cy.get('input[type="file"]').attachFile({
            fileContent,
            fileName: file.fileName,
            mimeType: file.mimeType,
            encoding: 'utf8',
          });
          return fileContent;
        });
    });

    // Upload the files
    cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();

    // Edit files (simplified for multiple files)
    cy.get('[data-test="checked-all-file"]').find('[type="checkbox"]').check();
    cy.get('[data-test="edit-file"]').click();
    cy.AddTagsUpload();
    cy.contains('General').click();
    cy.get('[data-test="save-edit-file"]').click();

    // Next step processing
    cy.NextStep();

    // Use simple cognitive workflow for multiple files
    cy.ClickSimpleCognitiveWorkflow();

    // Next step content template
    cy.NextStep();

    // Select folder e2e/upload
    cy.SelectFolder();

    // Add tags upload
    cy.AddTagsUpload();

    // Save upload files
    cy.NextStep();
    cy.contains('created successfully', { timeout: 60000 });
  }
);
