import { Before, Then, When } from '@badeball/cypress-cucumber-preprocessor';
import { uploadPage } from '../../../pages/uploadPage';

Before(() => {
  cy.LoginLandingPage();
  uploadPage.navigateToUploadFiles('e2e/upload');
});

When('The user opens the upload screen', () => {
  uploadPage.clickUploadButton();
});

Then('The screen is displayed correctly with all default elements', () => {
  uploadPage.verifyInitialUploadScreen();
});

When('The user uploads a video file for object detection', () => {
  uploadPage.uploadVideoFile();
});

When('The user uploads an audio file for transcription', () => {
  uploadPage.uploadAudioFile();
});

When('The user uploads an image file for facial detection', () => {
  uploadPage.uploadImage();
});

When('The user uploads multiple files for processing', () => {
  uploadPage.uploadMultipleFiles();
});

When('The user uploads a document file for text recognition', () => {
  uploadPage.uploadPDFFile();
});
