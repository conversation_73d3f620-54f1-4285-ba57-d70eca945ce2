import {
  categoryIdObjectDetection,
  engineIdMachineboxTagbox,
  categoryIdTextExtraction,
  engineIdTextExtraction,
  categoryIdTranscription,
  engineIdTranscription,
  categoryIdSpeakerDetection,
  engineIdSpeakerSeparation,
  categoryIdTranslate,
  engineIdTranslateSpanishToEnglish,
  veritoneAppId,
} from '../fixtures/variables';
import { deleteTDOByFilePath } from '../../src/state/modules/tdo/index';

export const uploadPage = {
  navigateToUploadFolder: (nameToDelete: string): void => {
    cy.LoginLandingPage().then(() => {
      // clean up all the file in that folder before upload test
      const endpoint = `${Cypress.env('apiRoot')}/v3/graphql`;
      const token = Cypress.env('token');
      cy.wrap(
        deleteTDOByFilePath(endpoint, token, veritoneAppId, nameToDelete)
      );
      return;
    });
    // navigate to e2e/upload folder
    cy.NavigateToUploadFolder();
  },
  navigateToUploadFolderAndDeleteFile: (): void => {
    cy.LoginLandingPage().then(() => {
      // clean up all the file in that folder before upload test
      const endpoint = `${Cypress.env('apiRoot')}/v3/graphql`;
      const token = Cypress.env('token');
      cy.wrap(
        deleteTDOByFilePath(
          endpoint,
          token,
          veritoneAppId,
          'e2e/upload/spanish-pdf.pdf'
        )
      );
      cy.wrap(
        deleteTDOByFilePath(
          endpoint,
          token,
          veritoneAppId,
          'e2e/upload/spanish-email.eml'
        )
      );
      return;
    });

    // navigate to e2e/upload folder
    cy.NavigateToUploadFolder();
  },
  uploadImage: (): void => {
    const imageName = 'image-plant-jpeg.jpeg';
    const imagePath = '../setup/image-plant-jpeg.jpeg';
    const mimeTypeImage = 'image/png';
    cy.UploadFileBasic(
      imageName,
      imagePath,
      mimeTypeImage,
      categoryIdObjectDetection,
      engineIdMachineboxTagbox
    );
  },
  uploadPDFFile: (): void => {
    const pdfName = 'spanish-pdf.pdf';
    const pdfPath = '../setup/spanish-pdf.pdf';
    const mimeTypePdf = 'application/pdf';
    cy.UploadFileBasic(
      pdfName,
      pdfPath,
      mimeTypePdf,
      categoryIdTextExtraction,
      engineIdTextExtraction
    );
  },
  uploadEMLFile: (): void => {
    const emlName = 'spanish-email.eml';
    const emlPath = '../setup/spanish-email.eml';
    const mimeTypeEml = 'message/rfc822';
    cy.UploadFileBasic(
      emlName,
      emlPath,
      mimeTypeEml,
      categoryIdTextExtraction,
      engineIdTextExtraction
    );
  },
  uploadVideoFile: (): void => {
    const fileName = 'bloomberg.mp4';
    const filePath = '../setup/bloomberg.mp4';
    const mimeType = 'video/mp4';
    const categoryEngines = [
      { categoryId: categoryIdTranscription, engineId: engineIdTranscription },
      {
        categoryId: categoryIdSpeakerDetection,
        engineId: engineIdSpeakerSeparation,
      },
    ];
    cy.UploadFileAdvanced(fileName, filePath, mimeType, categoryEngines);
  },
  uploadAudioFile: (): void => {
    const fileName = 'e2e_audio.mp3';
    const filePath = '../setup/e2e_audio.mp3';
    const mimeType = 'audio/mp3';
    const categoryEngines = [
      { categoryId: categoryIdTranscription, engineId: engineIdTranscription },
      {
        categoryId: categoryIdSpeakerDetection,
        engineId: engineIdSpeakerSeparation,
      },
    ];
    cy.UploadFileAdvanced(fileName, filePath, mimeType, categoryEngines);
  },
  UploadTextFile: (): void => {
    const textName = 'spanish-txt.txt';
    const textPath = '../setup/spanish-txt.txt';
    const mimeTypeText = 'text/plain';
    cy.UploadFileBasic(
      textName,
      textPath,
      mimeTypeText,
      categoryIdTranslate,
      engineIdTranslateSpanishToEnglish
    );
  },
  navigateToUploadFiles(folderPath: string): void {
    const folders = folderPath.split('/');
    cy.get('[data-test="top-bar-select-folder"]').click();
    folders.forEach((folderName) => {
      cy.get('[data-test="folder-modal-dialog-content"]')
        .find(`[data-testid="list-item-text"][aria-label="${folderName}"]`)
        .click();
    });
    cy.get('[data-test="folder-modal-select-folder-button"]').click();
    cy.get('[role="progressbar"]').should('not.exist');
    cy.get('[data-test="files-tab-button"]').click();
    cy.get('[data-testid^=files-table-row]').should('be.visible');
    cy.get('[role="progressbar"]').should('not.exist');
  },
  clickUploadButton(): void {
    cy.get('[data-test="upload-file"]').click();
  },
  verifyInitialUploadScreen(): void {
    cy.get('[role="dialog"]').within(() => {
      cy.contains('h6', 'Upload').should('be.visible');

      cy.get('[data-testid="stepper"]').within(() => {
        cy.contains('span', 'File Upload').should('have.class', 'Mui-active');
        cy.contains('span', 'Processing').should('have.class', 'Mui-disabled');
      });

      cy.get('[data-testid="cloud-upload"]').should('be.visible');
      cy.contains('div', 'Upload Media').should('be.visible');
      cy.contains(
        'div',
        'Select Video, Audio, Image, or Text files to upload'
      ).should('be.visible');
      cy.contains('span', 'Recommended file formats:')
        .parent()
        .should('contain.text', '.mp4, .mp3, .jpg, and .png');

      cy.get('[data-testid="back-button"]').should('be.disabled');
      cy.get('[data-testid="next-button"]').should('be.disabled');
    });
  },
};
