Feature: Upload

  @e2e @upload
  Scenario: The user verifies the initial state of the upload screen
    When The user opens the upload screen
    Then The screen is displayed correctly with all default elements

  @e2e @upload
  Scenario: Upload a video for object detection
    When The user uploads a video file for object detection

  @e2e @upload
  Scenario: Upload an audio file for transcription
    When The user uploads an audio file for transcription

  @e2e @upload
  Scenario: Upload an image for facial detection
    When The user uploads an image file for facial detection

  @e2e @upload
  Scenario: Upload a document file for text recognition
    When The user uploads a document file for text recognition
