declare namespace Cypress {
  interface UserInfo {
    body: {
      token: string;
      userId: string;
    };
  }

  // Remove CookiesStatic augmentation as preserveOnce is obsolete
  // and cy.session() is the recommended approach.
  interface Chainable {
    Graphql(
      query: string,
      variables?: unknown
    ): Chainable<Cypress.Response<any>>;
    request<T = unknown>(
      options: Partial<RequestOptions>
    ): Chainable<Response<T>>;
    LoginToApp(): Chainable;
    loginAsUser(userKey: string): Chainable;
    LoginLandingPage(): Chainable;
    LoginAndSetupSession(sessionId?: string): Chainable;
    NavigateToTestFolder(): Chainable;
    GoToTestFolder(): Chainable;
    NavigateToUploadFolder(): Chainable;
    NavigateToReprocessFolder(folderName: string): Chainable;
    RemoveTag(rowId: number, tagName: string): Chainable;
    FileSearch(query: object): Chainable;
    SearchAggregate(query: object): Chainable;
    OpenModalUploadFile(): Chainable;
    SelectFile(
      fileName: string,
      videoPath: string,
      mimeType: string
    ): Chainable<void>;
    ClickToUpload(): Chainable<void>;
    AddTagsUpload(): Chainable<void>;
    EditFile(fileName: string): Chainable<void>;
    FileUpload(
      fileName: string,
      videoPath: string,
      mimeType: string
    ): Chainable<void>;
    NextStep(): Chainable<void>;
    ClickEngineCategory(categoryIdTranscription: string): Chainable<void>;
    SelectAvailableEngine(
      categoryId: string,
      engineId: string
    ): Chainable<void>;
    SaveTemplateEngine(): Chainable<void>;
    AddContentTemplate(): Chainable<void>;
    ClickSimpleCognitiveWorkflow(): Chainable<void>;
    SelectFolder(): Chainable<void>;
    SelectLanguages(
      sourceLanguage: string,
      targetLanguage: string
    ): Chainable<void>;
    UploadFileBasic(
      fileName: string,
      filePath: string,
      mimeType: string,
      categoryId: string,
      engineId: string
    ): Chainable<void>;
    UploadFileAdvanced(
      fileName: string,
      filePath: string,
      mimeType: string,
      categoryEngines
    ): Chainable<void>;
    UploadFileImageWithEngineFacialDetection(
      fileName: string,
      videoPath: string,
      mimeType: string,
      categoryIdFacialDetection: string,
      engineIdFaceboxSimilarity: string
    ): Chainable<void>;
    ReprocessBasic(
      categoryId: string,
      engineId: string,
      fileName: string
    ): Chainable<void>;
    ReprocessAllWithEngineTranscription(
      categoryIdTranscription: string
    ): Chainable<void>;
    ReprocessFileVideoWithEngineAmazonTranslate(
      categoryIdTranslate: string,
      engineIdTranslate: string
    ): Chainable<void>;
    repeat({
      action,
      times,
    }: {
      action: unknown;
      times: number;
    }): Chainable<void>;
    awaitNetworkResponseCode({
      alias,
      code,
      repeat,
    }: {
      alias: string;
      code: number;
      repeat?: number;
    }): Chainable<void>;
  }
  interface RequestOptions extends Loggable, Timeoutable, Failable {
    auth: object;
    body: RequestBody;
    encoding: Encodings;
    followRedirect: boolean;
    form: boolean;
    gzip: boolean;
    headers: object;
    method: HttpMethod;
    qs: object;
    url: string;
  }
}

interface Organization {
  organizationId: string;
}

interface LoginResponse {
  organization: Organization;
  token: string;
  userId: string;
}
